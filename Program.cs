using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OpenApi;

var builder = WebApplication.CreateBuilder(args);

// Configuration (DON'T hardcode credentials in prod; use env vars)
var gbUser = builder.Configuration["GolfBox:Username"] ?? "Display4Media";
var gbPass = builder.Configuration["GolfBox:Password"] ?? "4Mf3E350mB4D";
var baseUrl = builder.Configuration["GolfBox:BaseUrl"] ?? "https://api.golfbox.net/Display4Media/Tour/";

// Services
builder.Services.AddHttpClient("GolfBox", client =>
{
    client.BaseAddress = new Uri(baseUrl);
    var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{gbUser}:{gbPass}"));
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    client.DefaultRequestHeaders.UserAgent.ParseAdd("GolfBoxProxy/1.0");
});

builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

var app = builder.Build();

app.UseSwagger();
app.UseSwaggerUI();

// Helpers
IResult TransformToItems(JsonElement root, string[] idKeys, string[] valueKeys, string? arrayKey = null)
{
    IEnumerable<JsonElement> elements;

    if (root.ValueKind == JsonValueKind.Array)
    {
        // e.g. Customers endpoint returns an array at the root
        elements = root.EnumerateArray();
    }
    else if (root.ValueKind == JsonValueKind.Object)
    {
        // specific array key (e.g. "classes")
        if (!string.IsNullOrWhiteSpace(arrayKey) &&
            root.TryGetProperty(arrayKey!, out var arrByKey) &&
            arrByKey.ValueKind == JsonValueKind.Array)
        {
            elements = arrByKey.EnumerateArray();
        }
        // common "items" holder
        else if (root.TryGetProperty("items", out var items) && items.ValueKind == JsonValueKind.Array)
        {
            elements = items.EnumerateArray();
        }
        else
        {
            // fallback: look for the first array property
            var firstArray = root.EnumerateObject()
                                 .Select(p => p.Value)
                                 .FirstOrDefault(v => v.ValueKind == JsonValueKind.Array);
            elements = firstArray.ValueKind == JsonValueKind.Array
                ? firstArray.EnumerateArray()
                : Array.Empty<JsonElement>();
        }
    }
    else
    {
        elements = Array.Empty<JsonElement>();
    }

    string? GetString(JsonElement e, IEnumerable<string> keys)
    {
        // exact names
        foreach (var k in keys)
        {
            if (e.ValueKind == JsonValueKind.Object &&
                e.TryGetProperty(k, out var val) &&
                (val.ValueKind == JsonValueKind.String || val.ValueKind == JsonValueKind.Number))
                return val.ToString();
        }
        // case-insensitive scan
        if (e.ValueKind == JsonValueKind.Object)
        {
            foreach (var p in e.EnumerateObject())
            {
                if (keys.Any(k => string.Equals(k, p.Name, StringComparison.OrdinalIgnoreCase)))
                    return p.Value.ToString();
            }
        }
        return null;
    }

    var mapped = elements
        .Select(el => new { id = GetString(el, idKeys), value = GetString(el, valueKeys) })
        .Where(x => x.id != null && x.value != null)
        .Select(x => new JsonObject
        {
            ["id"] = x.id,
            ["value"] = string.IsNullOrWhiteSpace(x.value) ? "not set" : x.value
        });

    var payload = new JsonObject
    {
        ["result"] = new JsonObject
        {
            ["items"] = new JsonArray(mapped.Select(m => (JsonNode)m).ToArray())
        }
    };
    return Results.Json(payload);
}


string ToYyyyMMdd(DateTime dt) => dt.ToString("yyyyMMdd");

// Endpoints

// GET /api/GetCustomers?country=SE
app.MapGet("/api/GetCustomers", async ([FromServices] IHttpClientFactory httpClientFactory, [FromQuery] string? country) =>
{
    var client = httpClientFactory.CreateClient("GolfBox");
    var url = "Customers";
    if (!string.IsNullOrWhiteSpace(country))
        url += $"?country={Uri.EscapeDataString(country)}";

    var resp = await client.GetAsync(url);
    var body = await resp.Content.ReadAsStringAsync();
    if (!resp.IsSuccessStatusCode) return Results.Problem(body, statusCode: (int)resp.StatusCode);

    using var doc = JsonDocument.Parse(body);
    // Map to { result.items: [{id,value}] }
    return TransformToItems(doc.RootElement,
        idKeys: new[] { "id", "customerid", "customerId", "CustomerID" },
        valueKeys: new[] { "value", "name", "customername", "customerName", "CustomerName" }
    );
})
.WithName("GetCustomers")
.Produces(200, contentType: "application/json");

// GET /api/GetCompetitionsForCustomer?customer=377&startdate=2025-07-01T00:00:00&enddate=2025-07-31T23:59:59
app.MapGet("/api/GetCompetitionsForCustomer", async ([FromServices] IHttpClientFactory httpClientFactory,
    [FromQuery] int customer, [FromQuery] DateTime startdate, [FromQuery] DateTime enddate) =>
{
    var client = httpClientFactory.CreateClient("GolfBox");
    var start = ToYyyyMMdd(startdate);
    var end = ToYyyyMMdd(enddate);
    var url = $"Customers/{customer}/Competitions?startdate={start}&enddate={end}";

    var resp = await client.GetAsync(url);
    var body = await resp.Content.ReadAsStringAsync();
    if (!resp.IsSuccessStatusCode) return Results.Problem(body, statusCode: (int)resp.StatusCode);

    using var doc = JsonDocument.Parse(body);
    return TransformToItems(doc.RootElement,
        idKeys: new[] { "id", "competitionid", "competitionId" },
        valueKeys: new[] { "value", "name", "title" }
    );
})
.WithName("GetCompetitionsForCustomer")
.WithOpenApi(op =>
{
    var startParam = op.Parameters.FirstOrDefault(p =>
        string.Equals(p.Name, "startdate", StringComparison.OrdinalIgnoreCase));
    var endParam = op.Parameters.FirstOrDefault(p =>
        string.Equals(p.Name, "enddate", StringComparison.OrdinalIgnoreCase));

    if (startParam != null) startParam.Description = "Start date (yyyy-MM-dd)";
    if (endParam != null) endParam.Description = "End date (yyyy-MM-dd)";
    return op;
})
.Produces(200, contentType: "application/json");

// GET /api/GetCompetitionClasses?competition=4998355
app.MapGet("/api/GetCompetitionClasses", async ([FromServices] IHttpClientFactory httpClientFactory, [FromQuery] int competition) =>
{
    var client = httpClientFactory.CreateClient("GolfBox");
    var url = $"Competitions/{competition}/Setup";

    var resp = await client.GetAsync(url);
    var body = await resp.Content.ReadAsStringAsync();
    if (!resp.IsSuccessStatusCode) return Results.Problem(body, statusCode: (int)resp.StatusCode);

    using var doc = JsonDocument.Parse(body);
    // many setups expose "classes" array
    return TransformToItems(doc.RootElement,
        idKeys: new[] { "id", "classid", "classId" },
        valueKeys: new[] { "value", "name", "classname", "className" },
        arrayKey: "classes"
    );
})
.WithName("GetCompetitionClasses")
.Produces(200, contentType: "application/json");

// Passthrough: GET /api/GetCompetitionResultsByClass?competition=...&classId=...
app.MapGet("/api/GetCompetitionResultsByClass", async ([FromServices] IHttpClientFactory httpClientFactory,
    [FromQuery] int competition, [FromQuery] int classId) =>
{
    var client = httpClientFactory.CreateClient("GolfBox");
    var url = $"Competitions/{competition}/Classes/{classId}/Leaderboard";

    var resp = await client.GetAsync(url);
    var body = await resp.Content.ReadAsStringAsync();
    return Results.Content(body, "application/json", statusCode: (int)resp.StatusCode);
})
.WithName("GetCompetitionResultsByClass")
.Produces(200, contentType: "application/json");

// Passthrough: GET /api/GetCompetitionStartList?competition=...
app.MapGet("/api/GetCompetitionStartList", async ([FromServices] IHttpClientFactory httpClientFactory,
    [FromQuery] int competition) =>
{
    var client = httpClientFactory.CreateClient("GolfBox");
    var url = $"Competitions/{competition}/Startlists?expand=teetimes";

    var resp = await client.GetAsync(url);
    var body = await resp.Content.ReadAsStringAsync();
    return Results.Content(body, "application/json", statusCode: (int)resp.StatusCode);
})
.WithName("GetCompetitionStartList")
.Produces(200, contentType: "application/json");

app.Run();
