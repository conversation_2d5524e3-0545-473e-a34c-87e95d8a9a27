{"info": {"name": "GolfBox Proxy API", "_postman_id": "b8e5bdf5-2a53-4f1e-9800-aaa", "description": "Postman collection to test the GolfBox Proxy API locally.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "GetCustomers", "request": {"method": "GET", "url": {"raw": "https://localhost:5001/api/GetCustomers?country=SE", "protocol": "https", "host": ["localhost"], "port": "5001", "path": ["api", "GetCustomers"], "query": [{"key": "country", "value": "SE"}]}}}, {"name": "GetCompetitionsForCustomer", "request": {"method": "GET", "url": {"raw": "https://localhost:5001/api/GetCompetitionsForCustomer?customer=377&startdate=2025-07-01T00:00:00&enddate=2025-07-31T23:59:59", "protocol": "https", "host": ["localhost"], "port": "5001", "path": ["api", "GetCompetitionsForCustomer"], "query": [{"key": "customer", "value": "377"}, {"key": "startdate", "value": "2025-07-01T00:00:00"}, {"key": "enddate", "value": "2025-07-31T23:59:59"}]}}}, {"name": "GetCompetitionClasses", "request": {"method": "GET", "url": {"raw": "https://localhost:5001/api/GetCompetitionClasses?competition=4998355", "protocol": "https", "host": ["localhost"], "port": "5001", "path": ["api", "GetCompetitionClasses"], "query": [{"key": "competition", "value": "4998355"}]}}}, {"name": "GetCompetitionResultsByClass", "request": {"method": "GET", "url": {"raw": "https://localhost:5001/api/GetCompetitionResultsByClass?competition=4998355&classId=4561701", "protocol": "https", "host": ["localhost"], "port": "5001", "path": ["api", "GetCompetitionResultsByClass"], "query": [{"key": "competition", "value": "4998355"}, {"key": "classId", "value": "4561701"}]}}}, {"name": "GetCompetitionStartList", "request": {"method": "GET", "url": {"raw": "https://localhost:5001/api/GetCompetitionStartList?competition=4998392", "protocol": "https", "host": ["localhost"], "port": "5001", "path": ["api", "GetCompetitionStartList"], "query": [{"key": "competition", "value": "4998392"}]}}}]}