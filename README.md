# GolfBox Proxy API

Proxy API to transform and expose data from the GolfBox system in a simplified JSON format for Spot/IXM.

## Quickstart

```bash
dotnet run
# Swagger UI: https://localhost:5001/swagger
```

> ⚠️ Move credentials to environment variables before deploying:
```
GolfBox__Username=...
GolfBox__Password=...
```

## Endpoints
- `GET /api/GetCustomers?country=SE` → transformed `{ result: { items: [{id,value}] } }`
- `GET /api/GetCompetitionsForCustomer?customer=377&startdate=2025-07-01T00:00:00&enddate=2025-07-31T23:59:59` → dates auto-converted to `yyyyMMdd`
- `GET /api/GetCompetitionClasses?competition=4998355` → transformed
- `GET /api/GetCompetitionResultsByClass?competition=...&classId=...` → passthrough
- `GET /api/GetCompetitionStartList?competition=...` → passthrough

## Config
- `appsettings.json` holds BaseUrl and credentials (dev only). Use env vars in prod.
