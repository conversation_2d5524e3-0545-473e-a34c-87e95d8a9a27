openapi: 3.0.3
info:
  title: GolfBox Proxy API
  version: 1.0.0
  description: >
    Proxy API to transform and expose data from the GolfBox system in a simplified JSON format.
    Uses Basic Authentication to forward requests to the original GolfBox API.
servers:
  - url: https://yourdomain.com
paths:
  /api/GetCustomers:
    get:
      tags: [Endpoints for configuration]
      summary: Get all customers filtered by country
      parameters:
        - in: query
          name: country
          required: false
          schema: { type: string, example: SE }
      responses:
        '200':
          description: List of customers (CustomerID and Name)
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ResultItemsResponse' }
  /api/GetCompetitionsForCustomer:
    get:
      tags: [Endpoints for configuration]
      summary: Get competitions for a customer within a date range
      parameters:
        - in: query
          name: customer
          required: true
          schema: { type: integer, example: 377 }
        - in: query
          name: startdate
          required: true
          schema: { type: string, format: date, example: 2024-11-07 }
        - in: query
          name: enddate
          required: true
          schema: { type: string, format: date, example: 2024-11-30 }
      responses:
        '200':
          description: List of competitions
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ResultItemsResponse' }
  /api/GetCompetitionClasses:
    get:
      tags: [Endpoints for configuration]
      summary: Get class list for a given competition
      parameters:
        - in: query
          name: competition
          required: true
          schema: { type: integer, example: 4998355 }
      responses:
        '200':
          description: List of classes
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ResultItemsResponse' }
  /api/GetCompetitionResultsByClass:
    get:
      tags: [Raw data]
      summary: Get leaderboard results for a class in a competition
      parameters:
        - in: query
          name: competition
          required: true
          schema: { type: integer, example: 4998355 }
        - in: query
          name: classId
          required: true
          schema: { type: integer, example: 4561701 }
      responses:
        '200':
          description: Raw leaderboard data (passthrough)
          content:
            application/json:
              schema: { type: object, description: Passthrough response from GolfBox }
  /api/GetCompetitionStartList:
    get:
      tags: [Raw data]
      summary: Get full startlist with tee times
      parameters:
        - in: query
          name: competition
          required: true
          schema: { type: integer, example: 4998392 }
      responses:
        '200':
          description: Raw startlist data (with tee times, passthrough)
          content:
            application/json:
              schema: { type: object, description: Passthrough response from GolfBox }
components:
  schemas:
    ResultItemsResponse:
      type: object
      properties:
        result:
          type: object
          properties:
            items:
              type: array
              items:
                type: object
                properties:
                  id: { type: string, example: "377" }
                  value: { type: string, example: "Djursholms Golfklubb" }
